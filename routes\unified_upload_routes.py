"""
Unified Upload Routes
Combines batch upload and enhanced upload functionality with unified ingestion pipeline
"""

import asyncio
import uuid
from pathlib import Path
from typing import List, Optional
from datetime import datetime
from fastapi import APIRouter, File, UploadFile, Form, BackgroundTasks, HTTPException, Depends
from pydantic import BaseModel

from utils.logging_utils import get_logger
from utils.file_utils import save_uploaded_file
from utils.config import get_config
from unified_ingestion_pipeline import get_unified_pipeline
from services.document_duplicate_detector import get_document_duplicate_detector
from utils.websocket_manager import get_websocket_manager

# Set up logger
logger = get_logger(__name__)

# Create router
router = APIRouter(prefix="/api/unified", tags=["unified-upload"])

# Global tracking
active_operations = {}
completed_operations = {}

class UnifiedUploadResponse(BaseModel):
    """Response model for unified upload."""
    success: bool
    message: str
    operation_id: Optional[str] = None
    filename: Optional[str] = None
    file_type: Optional[str] = None
    files_processed: Optional[int] = None
    operation_ids: Optional[List[str]] = None
    duplicate_detected: Optional[bool] = False
    duplicate_info: Optional[dict] = None

class ProgressTracker:
    """Track progress for upload operations."""
    
    def __init__(self, operation_id: str, filename: str):
        self.operation_id = operation_id
        self.filename = filename
        self.progress = 0
        self.status = "initializing"
        self.message = "Starting processing..."
        self.websocket_manager = get_websocket_manager()
    
    async def update_progress(self, progress: int, status: str, message: str):
        """Update progress and notify via WebSocket."""
        self.progress = progress
        self.status = status
        self.message = message

        # Send WebSocket update
        try:
            await self.websocket_manager.send_progress_update(self.operation_id, {
                'filename': self.filename,
                'progress': progress,
                'status': status,
                'message': message
            })
            logger.debug(f"✅ WebSocket update sent for {self.operation_id}: {progress}% - {status}")
        except Exception as e:
            logger.debug(f"⚠️ WebSocket update failed (no active connection): {e}")
            # Continue processing even if WebSocket fails - this is normal if UI isn't connected

        logger.info(f"📊 {self.filename}: {progress}% - {status} - {message}")
    
    async def complete(self, success: bool, result: dict = None):
        """Mark operation as complete."""
        status = "completed" if success else "failed"
        message = "Processing completed successfully" if success else "Processing failed"
        
        await self.update_progress(100, status, message)
        
        # Send completion notification (temporarily disabled to fix startup issues)
        try:
            await self.websocket_manager.send_completion(self.operation_id, {
                'filename': self.filename,
                'success': success,
                'result': result or {}
            })
        except Exception as e:
            logger.warning(f"⚠️ WebSocket completion failed: {e}")
        
        # Store final results and remove from active operations
        if self.operation_id in active_operations:
            # Store completed operation with final results
            completed_operations[self.operation_id] = {
                'operation_id': self.operation_id,
                'filename': self.filename,
                'progress': self.progress,
                'status': self.status,
                'message': self.message,
                'success': success,
                'result': result or {},
                'completed_at': datetime.now().isoformat()
            }
            # Remove from active operations
            del active_operations[self.operation_id]

@router.post("/upload-with-duplicate-check", response_model=UnifiedUploadResponse)
async def unified_upload_with_duplicate_check(
    background_tasks: BackgroundTasks,
    file: UploadFile = File(...),
    chunk_size: int = Form(1200),
    overlap: int = Form(0),
    extract_entities: bool = Form(True),
    extract_references: bool = Form(True),
    extract_metadata: bool = Form(True),
    generate_embeddings: bool = Form(True),
    skip_duplicate_check: bool = Form(False)
):
    """
    Unified upload with duplicate detection for single files.
    Supports all file types including OneNote (.one) files.
    """
    try:
        config = get_config()
        operation_id = str(uuid.uuid4())
        
        logger.info(f"🚀 Starting unified upload with duplicate check: {file.filename}")
        
        # Save uploaded file
        file_content = await file.read()
        file_path = save_uploaded_file(file_content, file.filename, config['paths']['uploads_dir'])
        logger.info(f"💾 Saved file: {file_path}")
        
        # Check for duplicates if not skipped
        if not skip_duplicate_check:
            duplicate_detector = await get_document_duplicate_detector()
            duplicate_result = await duplicate_detector.check_for_duplicates(str(file_path))

            if duplicate_result.is_duplicate:
                logger.info(f"🔍 Duplicate detected for: {file.filename}")
                return UnifiedUploadResponse(
                    success=True,
                    message="Duplicate document detected",
                    filename=file.filename,
                    file_type=file_path.suffix.lower(),
                    duplicate_detected=True,
                    duplicate_info=duplicate_result.dict()
                )
        
        # Create progress tracker
        tracker = ProgressTracker(operation_id, file.filename)
        active_operations[operation_id] = tracker
        
        # Start background processing
        background_tasks.add_task(
            process_single_file_unified,
            file_path,
            tracker,
            chunk_size,
            overlap,
            extract_entities,
            extract_references,
            extract_metadata,
            generate_embeddings
        )
        
        return UnifiedUploadResponse(
            success=True,
            message="File upload started successfully",
            operation_id=operation_id,
            filename=file.filename,
            file_type=file_path.suffix.lower()
        )
        
    except Exception as e:
        logger.error(f"❌ Error in unified upload with duplicate check: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/batch-upload", response_model=UnifiedUploadResponse)
async def unified_batch_upload(
    background_tasks: BackgroundTasks,
    files: List[UploadFile] = File(...),
    chunk_size: int = Form(1200),
    overlap: int = Form(0),
    extract_entities: bool = Form(True),
    extract_references: bool = Form(True),
    extract_metadata: bool = Form(True),
    generate_embeddings: bool = Form(True),
    max_parallel_processes: int = Form(4)
):
    """
    Unified batch upload for multiple files.
    Supports all file types including OneNote (.one) files.
    """
    try:
        config = get_config()
        operation_ids = []
        
        logger.info(f"🚀 Starting unified batch upload: {len(files)} files")
        
        # Save all files and create trackers
        file_paths = []
        trackers = []
        
        for file in files:
            try:
                # Save file
                file_content = await file.read()
                file_path = save_uploaded_file(file_content, file.filename, config['paths']['uploads_dir'])
                
                # Create tracker
                operation_id = str(uuid.uuid4())
                tracker = ProgressTracker(operation_id, file.filename)
                active_operations[operation_id] = tracker
                
                file_paths.append(file_path)
                trackers.append(tracker)
                operation_ids.append(operation_id)
                
                logger.info(f"💾 Saved file: {file_path}")
                
            except Exception as e:
                logger.error(f"❌ Error saving file {file.filename}: {e}")
                continue
        
        # Start background batch processing
        background_tasks.add_task(
            process_batch_files_unified,
            file_paths,
            trackers,
            chunk_size,
            overlap,
            extract_entities,
            extract_references,
            extract_metadata,
            generate_embeddings,
            max_parallel_processes
        )
        
        return UnifiedUploadResponse(
            success=True,
            message=f"Batch upload started successfully for {len(file_paths)} files",
            files_processed=len(file_paths),
            operation_ids=operation_ids
        )
        
    except Exception as e:
        logger.error(f"❌ Error in unified batch upload: {e}")
        raise HTTPException(status_code=500, detail=str(e))

async def process_single_file_unified(
    file_path: Path,
    tracker: ProgressTracker,
    chunk_size: int,
    overlap: int,
    extract_entities: bool,
    extract_references: bool,
    extract_metadata: bool,
    generate_embeddings: bool
):
    """Process a single file using the unified ingestion pipeline."""
    try:
        await tracker.update_progress(10, "processing", "Initializing unified pipeline...")
        
        # Get unified pipeline
        pipeline = await get_unified_pipeline()
        
        await tracker.update_progress(20, "processing", "Starting document processing...")
        await tracker.update_progress(30, "processing", "Extracting text from document...")

        # Process the document using unified pipeline
        result = await pipeline.process_document(
            file_path=file_path,
            chunk_size=chunk_size,
            overlap=overlap,
            extract_entities=extract_entities,
            extract_references=extract_references,
            extract_metadata=extract_metadata,
            generate_embeddings=generate_embeddings,
            force_reprocess=True
        )
        
        if result.get('success', False):
            await tracker.update_progress(90, "processing", "Finalizing...")
            
            # Log results
            logger.info(f"✅ Unified processing complete for {file_path.name}")
            logger.info(f"   📄 Text: {result.get('text_length', 0):,} chars")
            logger.info(f"   🧩 Chunks: {result.get('chunks', 0)}")
            logger.info(f"   🏷️ Entities: {result.get('entities', 0)}")
            logger.info(f"   📚 References: {result.get('references', 0)}")
            
            await tracker.complete(True, result)
        else:
            error_msg = result.get('error', 'Unknown error occurred')
            logger.error(f"❌ Unified processing failed for {file_path.name}: {error_msg}")
            await tracker.complete(False, {'error': error_msg})
            
    except Exception as e:
        logger.error(f"❌ Error processing file {file_path.name}: {e}")
        await tracker.complete(False, {'error': str(e)})

async def process_batch_files_unified(
    file_paths: List[Path],
    trackers: List[ProgressTracker],
    chunk_size: int,
    overlap: int,
    extract_entities: bool,
    extract_references: bool,
    extract_metadata: bool,
    generate_embeddings: bool,
    max_parallel_processes: int
):
    """Process multiple files in parallel using the unified ingestion pipeline."""
    try:
        logger.info(f"🔄 Starting batch processing of {len(file_paths)} files with {max_parallel_processes} parallel processes")
        
        # Create semaphore to limit parallel processing
        semaphore = asyncio.Semaphore(max_parallel_processes)
        
        async def process_with_semaphore(file_path: Path, tracker: ProgressTracker):
            async with semaphore:
                await process_single_file_unified(
                    file_path,
                    tracker,
                    chunk_size,
                    overlap,
                    extract_entities,
                    extract_references,
                    extract_metadata,
                    generate_embeddings
                )
        
        # Process all files in parallel (limited by semaphore)
        tasks = [
            process_with_semaphore(file_path, tracker)
            for file_path, tracker in zip(file_paths, trackers)
        ]
        
        await asyncio.gather(*tasks, return_exceptions=True)
        
        logger.info(f"✅ Batch processing complete for {len(file_paths)} files")
        
    except Exception as e:
        logger.error(f"❌ Error in batch processing: {e}")
        
        # Mark all remaining trackers as failed
        for tracker in trackers:
            if tracker.operation_id in active_operations:
                await tracker.complete(False, {'error': str(e)})

@router.get("/operations/{operation_id}")
async def get_operation_status(operation_id: str):
    """Get the status of a specific operation."""
    # Check active operations first
    if operation_id in active_operations:
        tracker = active_operations[operation_id]
        return {
            'operation_id': operation_id,
            'filename': tracker.filename,
            'progress': tracker.progress,
            'status': tracker.status,
            'message': tracker.message,
            'success': None,  # Still in progress
            'result': {}
        }
    # Check completed operations
    elif operation_id in completed_operations:
        return completed_operations[operation_id]
    else:
        raise HTTPException(status_code=404, detail="Operation not found")

@router.get("/operations")
async def get_all_operations():
    """Get the status of all active and completed operations."""
    return {
        'active_operations': [
            {
                'operation_id': op_id,
                'filename': tracker.filename,
                'progress': tracker.progress,
                'status': tracker.status,
                'message': tracker.message,
                'success': None,  # Still in progress
                'result': {}
            }
            for op_id, tracker in active_operations.items()
        ],
        'completed_operations': list(completed_operations.values())
    }
