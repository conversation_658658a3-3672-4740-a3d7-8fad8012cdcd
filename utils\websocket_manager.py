"""
WebSocket Manager for Real-time Progress Updates
Handles WebSocket connections for live progress tracking and cancellation
"""

import asyncio
import json
import logging
from typing import Dict, Set, Optional, Any
from fastapi import WebSocket, WebSocketDisconnect
from datetime import datetime
import uuid

logger = logging.getLogger(__name__)

class WebSocketManager:
    """
    Manages WebSocket connections for real-time progress updates.
    """
    
    def __init__(self):
        # Store active connections by operation_id
        self.active_connections: Dict[str, Set[WebSocket]] = {}
        # Store operation metadata
        self.operation_metadata: Dict[str, Dict[str, Any]] = {}
        # Store cancellation flags
        self.cancellation_flags: Dict[str, bool] = {}
        
    async def connect(self, websocket: WebSocket, operation_id: str):
        """
        Accept a new WebSocket connection for an operation.
        """
        await websocket.accept()
        
        if operation_id not in self.active_connections:
            self.active_connections[operation_id] = set()
        
        self.active_connections[operation_id].add(websocket)
        
        logger.info(f"WebSocket connected for operation {operation_id}. "
                   f"Total connections: {len(self.active_connections[operation_id])}")
        
        # Send initial connection confirmation
        await self.send_to_operation(operation_id, {
            "type": "connection_established",
            "operation_id": operation_id,
            "timestamp": datetime.utcnow().isoformat()
        })
    
    async def disconnect(self, websocket: WebSocket, operation_id: str):
        """
        Remove a WebSocket connection.
        """
        if operation_id in self.active_connections:
            self.active_connections[operation_id].discard(websocket)
            
            # Clean up if no more connections
            if not self.active_connections[operation_id]:
                del self.active_connections[operation_id]
                # Keep metadata for a while in case of reconnection
                
        logger.info(f"WebSocket disconnected for operation {operation_id}")
    
    async def send_to_operation(self, operation_id: str, data: Dict[str, Any]):
        """
        Send data to all WebSocket connections for a specific operation with optimized real-time performance.
        """
        if operation_id not in self.active_connections:
            return

        # Optimize JSON serialization for real-time updates
        try:
            message = json.dumps(data, separators=(',', ':'))  # Compact JSON for faster transmission
        except Exception as e:
            logger.error(f"Failed to serialize WebSocket message: {e}")
            return

        disconnected_websockets = []

        # Send to all connections concurrently for better real-time performance
        send_tasks = []
        for websocket in self.active_connections[operation_id].copy():
            send_tasks.append(self._send_to_websocket(websocket, message, disconnected_websockets))

        if send_tasks:
            await asyncio.gather(*send_tasks, return_exceptions=True)

        # Clean up disconnected websockets
        for websocket in disconnected_websockets:
            await self.disconnect(websocket, operation_id)

    async def _send_to_websocket(self, websocket, message: str, disconnected_list: list):
        """Send message to a single WebSocket with error handling."""
        try:
            await websocket.send_text(message)
        except Exception as e:
            logger.warning(f"Failed to send message to WebSocket: {e}")
            disconnected_list.append(websocket)
    
    async def send_progress_update(self, operation_id: str, progress_data: Dict[str, Any]):
        """
        Send a progress update to all connections for an operation.
        """
        # Enhanced progress update with detailed information
        update_message = {
            "type": "progress_update",
            "operation_id": operation_id,
            "data": progress_data,
            "timestamp": datetime.utcnow().isoformat()
        }

        # Add enhanced metadata for better UI updates
        if "statistics" in progress_data:
            stats = progress_data["statistics"]
            update_message["summary"] = {
                "characters": stats.get("total_characters", 0),
                "chunks": stats.get("total_chunks", 0),
                "entities": stats.get("total_entities", 0),
                "references": stats.get("total_references", 0),
                "embeddings": stats.get("total_embeddings", 0),
                "entities_by_chunk": stats.get("entities_by_chunk", [])
            }

        await self.send_to_operation(operation_id, update_message)
    
    async def send_completion(self, operation_id: str, final_data: Dict[str, Any]):
        """
        Send completion notification to all connections for an operation.
        """
        completion_message = {
            "type": "operation_complete",
            "operation_id": operation_id,
            "data": final_data,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        await self.send_to_operation(operation_id, completion_message)
        
        # Clean up after a delay
        await asyncio.sleep(5)  # Keep connection open for 5 seconds after completion
        if operation_id in self.active_connections:
            for websocket in self.active_connections[operation_id].copy():
                try:
                    await websocket.close()
                except:
                    pass
            try:
                del self.active_connections[operation_id]
            except KeyError:
                # Connection already cleaned up elsewhere
                pass
    
    async def send_error(self, operation_id: str, error_message: str, error_details: Optional[Dict] = None):
        """
        Send error notification to all connections for an operation.
        """
        error_message_data = {
            "type": "operation_error",
            "operation_id": operation_id,
            "error": error_message,
            "details": error_details or {},
            "timestamp": datetime.utcnow().isoformat()
        }
        
        await self.send_to_operation(operation_id, error_message_data)
    
    def set_cancellation_flag(self, operation_id: str):
        """
        Set cancellation flag for an operation.
        """
        self.cancellation_flags[operation_id] = True
        logger.info(f"Cancellation flag set for operation {operation_id}")
    
    def is_cancelled(self, operation_id: str) -> bool:
        """
        Check if an operation has been cancelled.
        """
        return self.cancellation_flags.get(operation_id, False)
    
    def clear_cancellation_flag(self, operation_id: str):
        """
        Clear cancellation flag for an operation.
        """
        self.cancellation_flags.pop(operation_id, None)
    
    async def cancel_operation(self, operation_id: str):
        """
        Cancel an operation and notify all connected clients.
        """
        self.set_cancellation_flag(operation_id)
        
        cancellation_message = {
            "type": "operation_cancelled",
            "operation_id": operation_id,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        await self.send_to_operation(operation_id, cancellation_message)
        
        # Close connections after a short delay
        await asyncio.sleep(2)
        if operation_id in self.active_connections:
            for websocket in self.active_connections[operation_id].copy():
                try:
                    await websocket.close()
                except:
                    pass
            del self.active_connections[operation_id]
    
    def get_active_operations(self) -> Dict[str, int]:
        """
        Get list of active operations and their connection counts.
        """
        return {
            operation_id: len(connections) 
            for operation_id, connections in self.active_connections.items()
        }
    
    def store_operation_metadata(self, operation_id: str, metadata: Dict[str, Any]):
        """
        Store metadata for an operation.
        """
        self.operation_metadata[operation_id] = {
            **metadata,
            "created_at": datetime.utcnow().isoformat()
        }
    
    def get_operation_metadata(self, operation_id: str) -> Optional[Dict[str, Any]]:
        """
        Get metadata for an operation.
        """
        return self.operation_metadata.get(operation_id)

# Global WebSocket manager instance
websocket_manager = WebSocketManager()

def get_websocket_manager() -> WebSocketManager:
    """
    Get the global WebSocket manager instance.
    """
    return websocket_manager
