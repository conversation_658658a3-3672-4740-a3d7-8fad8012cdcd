"""
Embedding processing service for the Graphiti application.

This service handles embedding generation for document facts.
"""

from typing import Dict, List, Any

from database.database_service import get_falkordb_adapter
from utils.logging_utils import get_logger

# OpenAI removed - using only Ollama for embeddings

# Import Ollama client for local embeddings
try:
    from utils.ollama_client import get_ollama_client
    OLLAMA_AVAILABLE = True
except ImportError:
    OLLAMA_AVAILABLE = False

# Set up logger
logger = get_logger(__name__)


class EmbeddingProcessor:
    """Service for processing embeddings for document facts."""

    def __init__(self):
        """Initialize the embedding processor - using only Ollama for embeddings."""
        self.ollama_client = None

        # Initialize Ollama client (only embedding provider)
        if OLLAMA_AVAILABLE:
            try:
                self.ollama_client = get_ollama_client()
                logger.info("✅ Ollama client initialized for embeddings")
            except Exception as e:
                logger.warning(f"❌ Could not initialize Ollama client: {e}")
        else:
            logger.error("❌ Ollama not available - embeddings will not work")

    async def generate_embeddings_for_document(self, episode_id: str, progress_tracker=None) -> Dict[str, Any]:
        """
        Generate embeddings for all facts in a document with real-time progress tracking.

        Args:
            episode_id: ID of the episode node
            progress_tracker: Optional progress tracker for real-time updates

        Returns:
            Result dictionary with embedding generation details
        """
        logger.info(f"Generating embeddings for document with episode ID: {episode_id}")

        try:
            # Get the adapter
            adapter = await get_falkordb_adapter()

            # Get facts for this episode that don't have embeddings
            query = f"""
            MATCH (e:Episode {{uuid: '{episode_id}'}})-[:CONTAINS]->(f:Fact)
            WHERE NOT EXISTS(f.embedding) OR f.embedding IS NULL OR f.embedding = ''
            RETURN f.uuid AS uuid, f.body AS body
            """

            result = adapter.execute_cypher(query)

            if not result or len(result) < 2 or not result[1]:
                logger.info(f"No facts without embeddings found for episode {episode_id}")
                return {
                    "success": True,
                    "embeddings_generated": 0,
                    "embedding_model": "none",
                    "message": "No facts need embeddings"
                }

            facts = result[1]
            logger.info(f"Found {len(facts)} facts without embeddings for episode {episode_id}")

            # Generate embeddings for each fact
            embeddings_generated = 0
            embedding_model = "none"

            # Generate embeddings using Ollama (only provider)
            if self.ollama_client:
                logger.info("🚀 Generating embeddings using Ollama...")
                embeddings_generated, embedding_model = await self._generate_embeddings_with_ollama(facts, adapter, progress_tracker)

                if embeddings_generated > 0:
                    logger.info(f"✅ Generated {embeddings_generated} embeddings using Ollama")
                    return {
                        "success": True,
                        "embeddings_generated": embeddings_generated,
                        "embedding_model": embedding_model
                    }
                else:
                    logger.warning("❌ Ollama embedding generation failed")
                    return {
                        "success": False,
                        "error": "Ollama embedding generation failed",
                        "embeddings_generated": 0,
                        "embedding_model": "none"
                    }
            else:
                logger.error("❌ Ollama client not available")
                return {
                    "success": False,
                    "error": "Ollama client not available - no embedding generation possible",
                    "embeddings_generated": 0,
                    "embedding_model": "none"
                }

        except Exception as e:
            logger.error(f"Error generating embeddings for document {episode_id}: {e}", exc_info=True)
            return {
                "success": False,
                "error": str(e),
                "embeddings_generated": 0,
                "embedding_model": "none"
            }

    async def _generate_embeddings_with_ollama(self, facts: List[List], adapter, progress_tracker=None) -> tuple[int, str]:
        """
        Generate embeddings using Ollama with real-time progress tracking.

        Args:
            facts: List of fact rows [uuid, body]
            adapter: Database adapter
            progress_tracker: Optional progress tracker for real-time updates

        Returns:
            Tuple of (embeddings_generated, model_name)
        """
        try:
            embeddings_generated = 0
            model_name = "snowflake-arctic-embed2"
            total_facts = len(facts)

            for i, fact_row in enumerate(facts):
                fact_uuid = fact_row[0]
                fact_body = fact_row[1]

                try:
                    # Generate embedding using Ollama
                    logger.debug(f"Generating embedding for fact {fact_uuid}: {fact_body[:100]}...")
                    embedding = self.ollama_client.generate_embeddings(
                        fact_body,
                        model=model_name
                    )

                    if embedding:
                        # Store embedding in database
                        success = await self._store_embedding(fact_uuid, embedding, adapter)
                        if success:
                            embeddings_generated += 1
                            logger.info(f"✅ Generated and stored embedding {embeddings_generated}/{total_facts}")

                            # Send real-time progress update
                            if progress_tracker:
                                try:
                                    # Update statistics with current embedding count
                                    progress_tracker.update_statistics(total_embeddings=embeddings_generated)

                                    # Send immediate progress update
                                    await progress_tracker._force_websocket_update(
                                        "progress_update",
                                        embeddings_progress=f"{embeddings_generated}/{total_facts}"
                                    )
                                except Exception as e:
                                    logger.debug(f"Progress update failed: {e}")

                except Exception as e:
                    logger.error(f"❌ Error generating embedding for fact {fact_uuid}: {e}")
                    continue

            return embeddings_generated, model_name

        except Exception as e:
            logger.error(f"Error in Ollama embedding generation: {e}")
            return 0, "none"

    async def _store_embedding(self, fact_uuid: str, embedding: List[float], adapter) -> bool:
        """
        Store an embedding in the database.

        Args:
            fact_uuid: UUID of the fact
            embedding: Embedding vector
            adapter: Database adapter

        Returns:
            True if successful, False otherwise
        """
        try:
            # Convert embedding to string format for storage
            embedding_str = str(embedding)

            # Update the fact with the embedding
            query = f"""
            MATCH (f:Fact {{uuid: '{fact_uuid}'}})
            SET f.embedding = '{embedding_str}'
            RETURN f.uuid
            """

            result = adapter.execute_cypher(query)

            if result and len(result) > 1 and len(result[1]) > 0:
                return True
            else:
                logger.error(f"Failed to store embedding for fact {fact_uuid}")
                return False

        except Exception as e:
            logger.error(f"Error storing embedding for fact {fact_uuid}: {e}")
            return False

    def get_embedding_stats(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """
        Get statistics about the embedding generation.

        Args:
            result: Result from embedding generation

        Returns:
            Dictionary with embedding statistics
        """
        try:
            stats = {
                "success": result.get("success", False),
                "embeddings_generated": result.get("embeddings_generated", 0),
                "embedding_model": result.get("embedding_model", "none"),
                "has_embeddings": result.get("embeddings_generated", 0) > 0
            }

            if "error" in result:
                stats["error"] = result["error"]
            if "message" in result:
                stats["message"] = result["message"]

            return stats

        except Exception as e:
            logger.error(f"Error getting embedding stats: {e}")
            return {
                "success": False,
                "embeddings_generated": 0,
                "embedding_model": "none",
                "has_embeddings": False,
                "error": str(e)
            }
