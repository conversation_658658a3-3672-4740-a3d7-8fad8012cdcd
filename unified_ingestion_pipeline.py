#!/usr/bin/env python3
"""
UNIFIED DOCUMENT INGESTION PIPELINE

This is the SINGLE, CONSOLIDATED pipeline for all document processing.
All other ingestion methods should route through this unified system.

Features:
- Single entry point for all document types
- Aggressive reference extraction by default
- Consistent processing across all input methods
- Proper CSV generation and UI integration
- No duplicate processing
"""

import asyncio
import os
import csv
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, List, Optional, Union
from utils.logging_utils import get_logger

logger = get_logger(__name__)

class UnifiedIngestionPipeline:
    """
    The ONE AND ONLY document ingestion pipeline.
    All document processing should go through this system.
    """
    
    def __init__(self):
        """Initialize the unified pipeline with the best components."""
        self.ocr_processor = None
        self.aggressive_extractor = None
        self.entity_extractor = None
        self.embedding_service = None
        self.database_adapter = None
        
        logger.info("🚀 Initializing UNIFIED INGESTION PIPELINE")
    
    async def _initialize_components(self):
        """Initialize all required components lazily."""
        if not self.ocr_processor:
            from utils.mistral_ocr import MistralOCRProcessor
            self.ocr_processor = MistralOCRProcessor()
            logger.info("✅ OCR processor initialized")
        
        if not self.aggressive_extractor:
            from services.improved_aggressive_reference_extractor import get_improved_aggressive_reference_extractor
            self.aggressive_extractor = await get_improved_aggressive_reference_extractor()
            logger.info("✅ IMPROVED Aggressive reference extractor initialized")
        
        if not self.entity_extractor:
            # Use the existing working entity extraction directly
            from entity_extraction.main import extract_entities_from_text
            self.entity_extractor = extract_entities_from_text
            logger.info("✅ Entity extractor initialized (using existing working module)")
        
        if not self.embedding_service:
            # Use the existing working embedding processor
            from services.embedding_processor import EmbeddingProcessor
            self.embedding_service = EmbeddingProcessor()
            logger.info("✅ Embedding service initialized (using existing working processor)")

        if not self.database_adapter:
            from database.falkordb_adapter import get_falkordb_adapter
            self.database_adapter = await get_falkordb_adapter()
            logger.info("✅ Database adapter initialized")
    
    async def process_document(
        self,
        file_path: Union[str, Path],
        chunk_size: int = 1200,
        overlap: int = 0,
        extract_entities: bool = True,
        extract_references: bool = True,
        extract_metadata: bool = True,
        generate_embeddings: bool = True,
        force_reprocess: bool = False,
        progress_tracker=None
    ) -> Dict[str, Any]:
        """
        Process a single document through the unified pipeline.
        
        This is the MAIN ENTRY POINT for all document processing.
        """
        file_path = Path(file_path)
        logger.info(f"🔄 UNIFIED PIPELINE: Processing {file_path.name}")
        
        # Initialize components
        await self._initialize_components()
        
        # Check if already processed (unless force reprocess)
        if not force_reprocess:
            existing = await self._check_if_already_processed(file_path)
            if existing:
                logger.info(f"✅ Document already processed: {file_path.name}")
                return existing
        
        try:
            # Import ProcessingStep if we have a progress tracker
            if progress_tracker:
                from utils.enhanced_progress_tracker import ProcessingStep

            # Step 1: Extract text using OCR
            if progress_tracker:
                await progress_tracker.start_step(ProcessingStep.TEXT_EXTRACTION, "Extracting text using OCR...")
            logger.info("📄 Step 1: Text extraction")
            text_result = await self._extract_text(file_path)

            if not text_result.get('success', False):
                error_msg = f"Text extraction failed: {text_result.get('error', 'Unknown error')}"
                if progress_tracker:
                    await progress_tracker.fail_step(ProcessingStep.TEXT_EXTRACTION, error_msg)
                return self._create_error_result(error_msg)

            extracted_text = text_result.get('text', '')
            if len(extracted_text) < 100:
                error_msg = "Extracted text too short"
                if progress_tracker:
                    await progress_tracker.fail_step(ProcessingStep.TEXT_EXTRACTION, error_msg)
                return self._create_error_result(error_msg)

            if progress_tracker:
                await progress_tracker.complete_step(ProcessingStep.TEXT_EXTRACTION,
                    f"Extracted {len(extracted_text):,} characters",
                    {"text_length": len(extracted_text)})
                progress_tracker.update_statistics(total_characters=len(extracted_text))

            # Step 2: Extract references using AGGRESSIVE method
            references_result = {'count': 0, 'csv_path': None}
            if extract_references:
                if progress_tracker:
                    await progress_tracker.start_step(ProcessingStep.REFERENCE_EXTRACTION, "Extracting references...")
                logger.info("📚 Step 2: Aggressive reference extraction")
                references_result = await self._extract_references_aggressive(file_path, extracted_text)

                if progress_tracker:
                    await progress_tracker.complete_step(ProcessingStep.REFERENCE_EXTRACTION,
                        f"Extracted {references_result.get('count', 0)} references",
                        {"references_count": references_result.get('count', 0)})
                    progress_tracker.update_statistics(total_references=references_result.get('count', 0))

            # Step 3: Entity extraction and chunk processing
            entities_result = {'count': 0}
            chunks_result = {'count': 0}

            if extract_entities:
                if progress_tracker:
                    await progress_tracker.start_step(ProcessingStep.ENTITY_EXTRACTION, "Extracting entities from chunks...")
                logger.info("🏷️ Step 3: Entity extraction")
                entities_result = await self._extract_entities_from_chunks(
                    extracted_text, file_path.name, chunk_size, overlap, progress_tracker
                )

                if progress_tracker:
                    await progress_tracker.complete_step(ProcessingStep.ENTITY_EXTRACTION,
                        f"Extracted {entities_result.get('count', 0)} entities",
                        {"entities_count": entities_result.get('count', 0)})
                    progress_tracker.update_statistics(total_entities=entities_result.get('count', 0))

            if generate_embeddings:
                if progress_tracker:
                    await progress_tracker.start_step(ProcessingStep.CHUNK_PROCESSING, "Processing chunks...")
                logger.info("🧩 Step 4: Chunk processing and embeddings")
                chunks_result = await self._process_chunks_with_existing_methods(
                    extracted_text, file_path, chunk_size, overlap, progress_tracker
                )

                if progress_tracker:
                    await progress_tracker.complete_step(ProcessingStep.CHUNK_PROCESSING,
                        f"Created {chunks_result.get('count', 0)} chunks",
                        {"chunks_count": chunks_result.get('count', 0)})
                    progress_tracker.update_statistics(total_chunks=chunks_result.get('count', 0))

                    # Start embedding generation step
                    await progress_tracker.start_step(ProcessingStep.EMBEDDING_GENERATION, "Generating embeddings...")
                    # Note: Embedding completion will be handled in the embedding processor

            # Step 5: Extract metadata
            metadata_result = {}
            if extract_metadata:
                if progress_tracker:
                    await progress_tracker.start_step(ProcessingStep.METADATA_EXTRACTION, "Extracting metadata...")
                logger.info("📋 Step 5: Metadata extraction")
                metadata_result = await self._extract_metadata(file_path, extracted_text)

                if progress_tracker:
                    await progress_tracker.complete_step(ProcessingStep.METADATA_EXTRACTION,
                        "Metadata extraction completed",
                        {"metadata_keys": list(metadata_result.keys()) if metadata_result else []})
            
            # Create final result
            embeddings_count = chunks_result.get('embeddings_generated', 0)
            result = {
                'success': True,
                'filename': file_path.name,
                'file_path': str(file_path),
                'text_length': len(extracted_text),
                'chunks': chunks_result.get('count', 0),
                'entities': entities_result.get('count', 0),
                'references': references_result.get('total_found', 0),
                'embeddings': embeddings_count,
                'reference_csv_path': references_result.get('csv_path'),
                'metadata': metadata_result,
                'processing_time': datetime.now().isoformat(),
                'pipeline_version': 'unified_v1.0_with_existing_modules'
            }

            logger.info(f"✅ UNIFIED PIPELINE COMPLETE: {file_path.name}")
            logger.info(f"   📄 Text: {len(extracted_text):,} chars")
            logger.info(f"   🧩 Chunks: {chunks_result.get('count', 0)}")
            logger.info(f"   🏷️ Entities: {entities_result.get('count', 0)}")
            logger.info(f"   📚 References: {references_result.get('total_found', 0)}")
            logger.info(f"   🔗 Embeddings: {embeddings_count}")
            
            return result
            
        except Exception as e:
            logger.error(f"❌ UNIFIED PIPELINE ERROR: {e}")
            return self._create_error_result(str(e))
    
    async def process_batch(
        self,
        file_paths: List[Union[str, Path]],
        max_parallel: int = 3,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Process multiple documents in parallel through the unified pipeline.
        """
        logger.info(f"🔄 UNIFIED BATCH PROCESSING: {len(file_paths)} documents")
        
        # Process in batches to avoid overwhelming the system
        results = []
        semaphore = asyncio.Semaphore(max_parallel)
        
        async def process_single(file_path):
            async with semaphore:
                return await self.process_document(file_path, **kwargs)
        
        # Create tasks for all files
        tasks = [process_single(fp) for fp in file_paths]
        
        # Execute with progress tracking
        for i, task in enumerate(asyncio.as_completed(tasks), 1):
            result = await task
            results.append(result)
            logger.info(f"📊 Batch progress: {i}/{len(file_paths)} completed")
        
        # Summarize results
        successful = [r for r in results if r.get('success', False)]
        failed = [r for r in results if not r.get('success', False)]
        
        total_refs = sum(r.get('references', 0) for r in successful)
        total_entities = sum(r.get('entities', 0) for r in successful)
        total_chunks = sum(r.get('chunks', 0) for r in successful)
        
        summary = {
            'success': True,
            'total_documents': len(file_paths),
            'successful_count': len(successful),
            'failed_count': len(failed),
            'total_references': total_refs,
            'total_entities': total_entities,
            'total_chunks': total_chunks,
            'results': results,
            'processing_time': datetime.now().isoformat()
        }
        
        logger.info(f"🎉 UNIFIED BATCH COMPLETE:")
        logger.info(f"   ✅ Successful: {len(successful)}/{len(file_paths)}")
        logger.info(f"   📚 Total references: {total_refs}")
        logger.info(f"   🏷️ Total entities: {total_entities}")
        
        return summary
    
    async def _extract_text(self, file_path: Path) -> Dict[str, Any]:
        """Extract text using the best OCR method."""
        try:
            if file_path.suffix.lower() == '.pdf':
                return await self.ocr_processor.process_pdf(str(file_path))
            elif file_path.suffix.lower() in ['.txt', '.md']:
                # For text files, read directly
                with open(file_path, 'r', encoding='utf-8') as f:
                    text = f.read()
                return {'success': True, 'text': text, 'metadata': {'file_type': 'text'}}
            else:
                # For other files, use Mistral OCR processor
                return await self.ocr_processor.process_pdf(str(file_path))
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    async def _extract_references_aggressive(self, file_path: Path, text: str) -> Dict[str, Any]:
        """Extract references using the AGGRESSIVE method (the best one)."""
        try:
            # Use the aggressive extractor
            result = await self.aggressive_extractor.extract_references_from_text(text, file_path.name)
            
            # Save to CSV
            csv_path = await self._save_references_to_csv(result, file_path)
            
            return {
                'count': result.get('total_found', 0),
                'csv_path': csv_path,
                'confidence': result.get('confidence_score', 0.0),
                'methods': result.get('extraction_methods', {}),
                'success': True
            }
        except Exception as e:
            logger.error(f"❌ Aggressive reference extraction failed: {e}")
            return {'count': 0, 'csv_path': None, 'success': False, 'error': str(e)}
    
    async def _save_references_to_csv(self, ref_result: Dict[str, Any], file_path: Path) -> Optional[str]:
        """Save references to CSV file."""
        try:
            # Create references directory
            ref_dir = Path("references")
            ref_dir.mkdir(exist_ok=True)
            
            # Generate CSV filename
            doc_id = file_path.stem.split('_')[0] if '_' in file_path.stem else file_path.stem
            csv_filename = f"{doc_id}_{file_path.stem}_aggressive_references.csv"
            csv_path = ref_dir / csv_filename
            
            # Get detailed references
            detailed_refs = ref_result.get('detailed_references', [])
            if not detailed_refs:
                # Fallback to simple references
                simple_refs = ref_result.get('references', [])
                detailed_refs = [{'text': ref, 'extraction_method': 'aggressive'} for ref in simple_refs]
            
            # Write CSV
            with open(csv_path, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = [
                    'reference_number', 'text', 'authors', 'title', 'journal', 
                    'year', 'doi', 'pmid', 'url', 'confidence', 
                    'extraction_method', 'source_section'
                ]
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()
                
                for i, ref in enumerate(detailed_refs, 1):
                    writer.writerow({
                        'reference_number': ref.get('number', i),
                        'text': ref.get('text', ''),
                        'authors': ref.get('authors', ''),
                        'title': ref.get('title', ''),
                        'journal': ref.get('journal', ''),
                        'year': ref.get('year', ''),
                        'doi': ref.get('doi', ''),
                        'pmid': ref.get('pmid', ''),
                        'url': ref.get('url', ''),
                        'confidence': ref.get('confidence', 0.0),
                        'extraction_method': ref.get('extraction_method', 'aggressive'),
                        'source_section': ref.get('source_section', 'document')
                    })
            
            logger.info(f"💾 References saved to: {csv_filename}")
            return str(csv_path)
            
        except Exception as e:
            logger.error(f"❌ Error saving references CSV: {e}")
            return None
    
    async def _extract_entities(self, text: str, filename: str) -> Dict[str, Any]:
        """Extract entities from text."""
        try:
            if self.entity_extractor:
                logger.info(f"🔍 Calling entity extractor with text length: {len(text)}")

                # Use the entity extraction function with correct parameters
                entities = await self.entity_extractor(
                    text=text,
                    document_id=filename,
                    fact_id=None,
                    llm_provider='openrouter'
                )

                logger.info(f"🏷️ Entity extractor returned: {len(entities) if entities else 0} entities")

                # Store entities in knowledge graph if we have them
                if entities and len(entities) > 0:
                    logger.info(f"💾 Storing {len(entities)} entities in knowledge graph")
                    await self._store_entities_in_graph(entities, filename)
                else:
                    logger.warning(f"⚠️ No entities to store (entities: {entities})")

                return {
                    'count': len(entities) if entities else 0,
                    'success': True,
                    'entities': entities
                }
            else:
                logger.warning("⚠️ No entity extractor available")
                return {'count': 0, 'success': True}
        except Exception as e:
            logger.error(f"❌ Entity extraction failed: {e}")
            import traceback
            traceback.print_exc()
            return {'count': 0, 'success': False, 'error': str(e)}

    async def _extract_entities_from_chunks(
        self,
        text: str,
        filename: str,
        chunk_size: int,
        overlap: int,
        progress_tracker=None
    ) -> Dict[str, Any]:
        """Extract entities from individual chunks and link them to facts."""
        try:
            total_entities = 0

            # Skip extracting from the full document - only extract from chunks
            logger.info(f"🔍 Extracting entities from chunks only (skipping full document of {len(text):,} chars)")

            # Get the episode ID for this document
            logger.info(f"🔍 Looking for episode ID for document: {filename}")
            episode_id = await self._get_episode_id_for_document(filename)
            if not episode_id:
                logger.error(f"❌ Could not find episode ID for document: {filename}")
                return {'count': 0, 'success': False, 'error': 'Episode not found'}

            logger.info(f"✅ Found episode ID: {episode_id}")

            # Get the facts (chunks) for this episode
            facts = await self._get_facts_for_episode(episode_id)
            if not facts:
                logger.error(f"❌ No facts found for episode: {episode_id}")
                return {'count': 0, 'success': False, 'error': 'No facts found'}

            logger.info(f"✅ Found {len(facts)} facts for episode: {episode_id}")

            # 2. Extract entities from chunks and link to facts
            logger.info(f"📄 Processing {len(facts)} facts for entity extraction")

            chunk_entities_total = 0
            for i, fact in enumerate(facts):
                fact_uuid = fact['uuid']
                fact_body = fact['body']
                chunk_index = fact.get('chunk_index', i)

                if len(fact_body.strip()) > 100 and self.entity_extractor:  # Only process substantial chunks
                    try:
                        logger.info(f"🔍 Extracting entities from fact {i+1}/{len(facts)}: {fact_uuid[:8]}...")

                        chunk_entities = await self.entity_extractor(
                            api_key=None,  # Will use environment variable
                            text=fact_body
                        )

                        if chunk_entities:
                            logger.info(f"   📊 Extracted {len(chunk_entities)} entities from fact {i+1}")

                            # Use the existing working entity extraction service
                            success = await self._link_entities_to_fact_using_working_method(
                                fact_uuid,
                                chunk_entities
                            )

                            if success:
                                chunk_entities_total += len(chunk_entities)
                                logger.info(f"   ✅ Fact {i+1}: {len(chunk_entities)} entities linked successfully")
                            else:
                                logger.warning(f"   ⚠️ Fact {i+1}: Failed to link {len(chunk_entities)} entities")

                            # Update progress tracker with chunk entity count
                            if progress_tracker:
                                progress_tracker.add_chunk_entity_count(chunk_index, len(chunk_entities))

                    except Exception as e:
                        logger.warning(f"   ⚠️ Fact {i+1} entity extraction failed: {e}")
                        continue
                else:
                    logger.debug(f"   ⏭️ Skipping fact {i+1} (too short or no extractor)")

            total_entities = chunk_entities_total
            logger.info(f"✅ Total entities extracted and linked: {total_entities} (using proper fact-entity linking)")

            return {
                'count': total_entities,
                'success': True,
                'document_entities': 0,  # No document-level extraction
                'chunk_entities': chunk_entities_total,
                'chunks_processed': len(facts)
            }

        except Exception as e:
            logger.error(f"❌ Entity extraction from chunks failed: {e}")
            return {'count': 0, 'success': False, 'error': str(e)}

    async def _get_episode_id_for_document(self, filename: str) -> Optional[str]:
        """Get the episode ID for a document by filename."""
        try:
            if not self.database_adapter:
                logger.error("❌ No database adapter available")
                return None

            query = """
            MATCH (ep:Episode)
            WHERE ep.name CONTAINS $filename
            RETURN ep.uuid as uuid
            ORDER BY ep.processed_at DESC
            LIMIT 1
            """

            result = self.database_adapter.execute_cypher(query, {'filename': filename})

            if result and len(result) > 1 and result[1]:
                episode_id = result[1][0][0]
                logger.info(f"✅ Found episode ID: {episode_id} for document: {filename}")
                return episode_id
            else:
                logger.error(f"❌ No episode found for document: {filename}")
                return None

        except Exception as e:
            logger.error(f"❌ Error getting episode ID for document {filename}: {e}")
            return None

    async def _get_facts_for_episode(self, episode_id: str) -> List[Dict[str, Any]]:
        """Get all facts for an episode."""
        try:
            if not self.database_adapter:
                logger.error("❌ No database adapter available")
                return []

            query = """
            MATCH (ep:Episode {uuid: $episode_id})-[:CONTAINS]->(f:Fact)
            RETURN f.uuid as uuid, f.body as body, f.chunk_index as chunk_index
            ORDER BY f.chunk_index
            """

            result = self.database_adapter.execute_cypher(query, {'episode_id': episode_id})

            if result and len(result) > 1 and result[1]:
                facts = []
                for row in result[1]:
                    facts.append({
                        'uuid': row[0],
                        'body': row[1],
                        'chunk_index': row[2] if len(row) > 2 else 0
                    })
                logger.info(f"✅ Found {len(facts)} facts for episode: {episode_id}")
                return facts
            else:
                logger.error(f"❌ No facts found for episode: {episode_id}")
                return []

        except Exception as e:
            logger.error(f"❌ Error getting facts for episode {episode_id}: {e}")
            return []

    async def _store_entities_and_link_to_fact(
        self,
        entities: List[Dict[str, Any]],
        episode_id: str,
        chunk_index: int,
        filename: str
    ) -> bool:
        """Store entities and link them to the corresponding fact using EntityProcessor."""
        try:
            # Get the fact UUID for this chunk
            facts = await self._get_facts_for_episode(episode_id)
            if chunk_index >= len(facts):
                logger.error(f"❌ Chunk index {chunk_index} out of range for {len(facts)} facts")
                return False

            fact_uuid = facts[chunk_index]['uuid']

            # Use the proper EntityProcessor to create entities and link to fact
            from entity_extraction.processors.entity_processor import EntityProcessor
            from database.falkordb_adapter import get_falkordb_adapter

            # EntityProcessor expects a driver, but we need to adapt FalkorDB
            # For now, let's use the direct database approach
            adapter = await get_falkordb_adapter()

            # Create entities directly using the database adapter with proper MENTIONS relationships
            success = await self._create_entities_with_mentions_relationships(fact_uuid, entities, adapter)

            if success:
                logger.info(f"✅ Linked {len(entities)} entities to fact {fact_uuid}")
                return True
            else:
                logger.error(f"❌ Failed to link entities to fact {fact_uuid}")
                return False

        except Exception as e:
            logger.error(f"❌ Error storing entities and linking to fact: {e}")
            return False

    async def _link_entities_to_fact_using_working_method(
        self,
        fact_uuid: str,
        entities: List[Dict[str, Any]]
    ) -> bool:
        """Link entities to fact using the existing working entity extraction service."""
        try:
            # Use the existing working entity extraction service
            from services.entity_extraction_service import extract_entities_from_text

            # The working service expects to be called with text and fact_id
            # Since we already have the entities, we need to use the direct linking approach
            # Let's use the working pattern from services.entity_processor

            from services.entity_processor import EntityProcessor

            processor = EntityProcessor()

            # Get the episode_id for this fact
            adapter = await get_falkordb_adapter()
            episode_query = f"""
            MATCH (ep:Episode)-[:CONTAINS]->(f:Fact {{uuid: '{fact_uuid}'}})
            RETURN ep.uuid as episode_id
            """
            episode_result = adapter.execute_cypher(episode_query)

            if not episode_result or len(episode_result) <= 1 or not episode_result[1]:
                logger.error(f"Could not find episode for fact {fact_uuid}")
                return False

            episode_id = episode_result[1][0][0]

            # Use the working entity creation method
            created_count = 0
            for entity in entities:
                try:
                    entity_created = await processor._create_entity_node(
                        entity, episode_id, fact_uuid, adapter
                    )
                    if entity_created:
                        created_count += 1
                        logger.debug(f"   ✅ Created entity: {entity.get('name')} ({entity.get('type')})")
                    else:
                        logger.warning(f"   ⚠️ Failed to create entity: {entity.get('name')}")

                except Exception as e:
                    logger.warning(f"   ⚠️ Error creating entity {entity.get('name', 'unknown')}: {e}")
                    continue

            logger.info(f"✅ Created {created_count}/{len(entities)} entities using working method")
            return created_count > 0

        except Exception as e:
            logger.error(f"❌ Error linking entities using working method: {e}")
            return False

    async def _process_chunks_with_existing_methods(
        self,
        text: str,
        file_path: Path,
        chunk_size: int,
        overlap: int,
        progress_tracker=None
    ) -> Dict[str, Any]:
        """Process chunks using existing working methods and store in Redis."""
        try:
            # Create chunks
            chunks = self._create_text_chunks(text, chunk_size, overlap)
            logger.info(f"📄 Created {len(chunks)} chunks for processing")

            if not chunks:
                return {'count': 0, 'success': True, 'chunks_created': 0}

            # First, we need to store the chunks as Facts in FalkorDB
            # Then use the existing embedding processor to generate embeddings and store in Redis

            episode_id = await self._create_episode_and_facts(file_path, chunks)

            if episode_id and self.embedding_service:
                logger.info(f"🔄 Generating embeddings for {len(chunks)} chunks using existing processor")

                # Use the existing embedding processor with progress tracking
                embedding_result = await self.embedding_service.generate_embeddings_for_document(
                    episode_id,
                    progress_tracker=progress_tracker
                )

                embeddings_generated = embedding_result.get('embeddings_generated', 0)
                logger.info(f"✅ Generated {embeddings_generated} embeddings and stored in Redis")

                # Complete embedding generation step if progress tracker is available
                if progress_tracker:
                    try:
                        from utils.enhanced_progress_tracker import ProcessingStep
                        await progress_tracker.complete_step(
                            ProcessingStep.EMBEDDING_GENERATION,
                            f"Generated {embeddings_generated} embeddings",
                            {"embeddings_count": embeddings_generated}
                        )
                        progress_tracker.update_statistics(total_embeddings=embeddings_generated)
                        logger.info(f"✅ Updated progress tracker with {embeddings_generated} embeddings")
                    except Exception as e:
                        logger.warning(f"Failed to update progress tracker: {e}")

                return {
                    'count': len(chunks),
                    'success': True,
                    'chunks_created': len(chunks),
                    'embeddings_generated': embeddings_generated,
                    'episode_id': episode_id
                }
            else:
                logger.warning("⚠️ No embedding service available or episode creation failed")
                return {
                    'count': len(chunks),
                    'success': True,
                    'chunks_created': len(chunks),
                    'embeddings_generated': 0
                }

        except Exception as e:
            logger.error(f"❌ Chunk processing failed: {e}")
            return {'count': 0, 'success': False, 'error': str(e)}

    def _create_text_chunks(self, text: str, chunk_size: int, overlap: int) -> List[str]:
        """Create text chunks using the existing working approach."""
        if chunk_size <= 0:
            return [text]

        chunks = []
        start = 0

        while start < len(text):
            end = start + chunk_size
            chunk = text[start:end]
            chunks.append(chunk)

            if end >= len(text):
                break

            start = end - overlap

        return chunks

    async def _create_episode_and_facts(self, file_path: Path, chunks: List[str]) -> Optional[str]:
        """Create episode and facts in FalkorDB for chunk processing."""
        try:
            if not self.database_adapter:
                logger.error("❌ No database adapter available")
                return None

            import uuid
            from datetime import datetime

            # Generate episode ID
            episode_id = str(uuid.uuid4())

            # Create episode
            episode_query = """
            CREATE (e:Episode {
                uuid: $episode_id,
                name: $filename,
                file_path: $file_path,
                processed_at: timestamp(),
                chunk_count: $chunk_count,
                processing_method: 'unified_pipeline'
            })
            RETURN e.uuid
            """

            try:
                result = self.database_adapter.execute_cypher(episode_query, {
                    'episode_id': episode_id,
                    'filename': file_path.name,
                    'file_path': str(file_path),
                    'chunk_count': len(chunks)
                })

                logger.info(f"📊 Episode creation result: {result}")

                if not result:
                    logger.error("❌ Failed to create episode - no result returned")
                    return None

                logger.info(f"✅ Created episode: {episode_id}")

                # Verify episode was created
                verify_query = "MATCH (e:Episode {uuid: $episode_id}) RETURN e.uuid"
                verify_result = self.database_adapter.execute_cypher(verify_query, {'episode_id': episode_id})
                logger.info(f"📊 Episode verification result: {verify_result}")

            except Exception as e:
                logger.error(f"❌ Error executing episode creation query: {e}")
                return None

            # Create facts for each chunk
            facts_created = 0
            for i, chunk in enumerate(chunks):
                try:
                    fact_id = str(uuid.uuid4())

                    fact_query = """
                    MATCH (e:Episode {uuid: $episode_id})
                    CREATE (f:Fact {
                        uuid: $fact_id,
                        body: $chunk_text,
                        chunk_index: $chunk_index,
                        created_at: timestamp()
                    })
                    CREATE (e)-[:CONTAINS]->(f)
                    RETURN f.uuid
                    """

                    fact_result = self.database_adapter.execute_cypher(fact_query, {
                        'episode_id': episode_id,
                        'fact_id': fact_id,
                        'chunk_text': chunk,
                        'chunk_index': i
                    })

                    if fact_result:
                        facts_created += 1
                        logger.debug(f"✅ Created fact {i+1}/{len(chunks)}: {fact_id}")
                    else:
                        logger.warning(f"⚠️ Failed to create fact {i+1}/{len(chunks)}")

                except Exception as e:
                    logger.error(f"❌ Error creating fact {i+1}/{len(chunks)}: {e}")

            logger.info(f"✅ Created {facts_created}/{len(chunks)} facts for episode {episode_id}")
            return episode_id

        except Exception as e:
            logger.error(f"❌ Error creating episode and facts: {e}")
            return None

    async def _process_with_existing_service(
        self,
        file_path: Path,
        chunk_size: int,
        overlap: int,
        extract_entities: bool,
        generate_embeddings: bool
    ) -> Dict[str, Any]:
        """Use the existing working document processing service."""
        try:
            if not hasattr(self, 'document_processor'):
                logger.warning("⚠️ Document processor not initialized")
                return {'entities': 0, 'chunks': 0, 'success': False}

            logger.info(f"🔄 Using existing document processing service for: {file_path.name}")

            # Use the existing working document processing service
            result = await self.document_processor.process_document(
                file_path=file_path,
                chunk_size=chunk_size,
                overlap=overlap,
                extract_entities=extract_entities,
                extract_references=False,  # We handle references separately
                extract_metadata=False,    # We handle metadata separately
                generate_embeddings=generate_embeddings
            )

            entities_count = result.get('entities_result', {}).get('entities_extracted', 0)
            chunks_count = result.get('chunks_result', {}).get('chunks_created', 0)

            logger.info(f"✅ Existing service processed: {entities_count} entities, {chunks_count} chunks")

            return {
                'entities': entities_count,
                'chunks': chunks_count,
                'success': result.get('success', False),
                'full_result': result
            }

        except Exception as e:
            logger.error(f"❌ Error using existing document processing service: {e}")
            return {'entities': 0, 'chunks': 0, 'success': False, 'error': str(e)}

    async def _store_entities_in_graph(self, entities: List[Dict[str, Any]], filename: str, source: str = "document") -> bool:
        """Store extracted entities in the knowledge graph."""
        try:
            if not self.database_adapter:
                logger.warning("⚠️ No database adapter available for entity storage")
                return False

            stored_count = 0
            for entity in entities:
                try:
                    # Extract entity information
                    entity_name = entity.get('name', '')
                    entity_type = entity.get('type', 'Unknown')
                    description = entity.get('description', '')
                    confidence = entity.get('confidence', 0.0)

                    if not entity_name:
                        continue

                    # Create entity in knowledge graph
                    query = """
                    MERGE (e:Entity {name: $name, type: $type})
                    SET e.description = $description,
                        e.confidence = $confidence,
                        e.source_document = $filename,
                        e.source_type = $source,
                        e.created_at = timestamp(),
                        e.extraction_method = 'llm_unified_pipeline'
                    RETURN e
                    """

                    result = self.database_adapter.execute_cypher(query, {
                        'name': entity_name,
                        'type': entity_type,
                        'description': description,
                        'confidence': confidence,
                        'filename': filename,
                        'source': source
                    })

                    if result:
                        stored_count += 1

                except Exception as e:
                    logger.warning(f"⚠️ Failed to store entity {entity.get('name', 'unknown')}: {e}")
                    continue

            logger.info(f"✅ Stored {stored_count}/{len(entities)} entities in knowledge graph")
            return stored_count > 0

        except Exception as e:
            logger.error(f"❌ Error storing entities in graph: {e}")
            return False

    async def _store_in_knowledge_graph(self, file_path: Path, text: str, chunk_size: int, overlap: int) -> Dict[str, Any]:
        """Store document in knowledge graph with embeddings and chunk-level entity extraction."""
        try:
            logger.info(f"📊 Creating chunks and extracting entities per chunk")

            # Create text chunks
            chunks = self._create_text_chunks(text, chunk_size, overlap)
            logger.info(f"📄 Created {len(chunks)} chunks from document")

            total_chunk_entities = 0
            chunk_results = []

            # Process each chunk for entity extraction
            for i, chunk in enumerate(chunks, 1):
                try:
                    logger.info(f"🔍 Processing chunk {i}/{len(chunks)} (length: {len(chunk)})")

                    # Extract entities from this chunk
                    if self.entity_extractor and len(chunk.strip()) > 50:  # Only process substantial chunks
                        chunk_entities = await self.entity_extractor(
                            text=chunk,
                            document_id=f"{file_path.name}_chunk_{i}",
                            fact_id=None,
                            llm_provider='openrouter'
                        )

                        if chunk_entities:
                            # Store chunk entities with chunk reference
                            await self._store_chunk_entities_in_graph(
                                chunk_entities,
                                file_path.name,
                                i,
                                chunk
                            )
                            total_chunk_entities += len(chunk_entities)
                            logger.info(f"   ✅ Chunk {i}: {len(chunk_entities)} entities extracted")
                        else:
                            logger.info(f"   ℹ️ Chunk {i}: No entities found")
                    else:
                        logger.info(f"   ⏭️ Chunk {i}: Skipped (too short or no extractor)")

                    chunk_results.append({
                        'chunk_id': i,
                        'length': len(chunk),
                        'entities_count': len(chunk_entities) if chunk_entities else 0
                    })

                except Exception as e:
                    logger.warning(f"⚠️ Error processing chunk {i}: {e}")
                    chunk_results.append({
                        'chunk_id': i,
                        'length': len(chunk),
                        'entities_count': 0,
                        'error': str(e)
                    })
                    continue

            logger.info(f"✅ Chunk processing complete: {total_chunk_entities} total chunk entities")

            return {
                'count': len(chunks),
                'success': True,
                'chunks_processed': len(chunks),
                'total_chunk_entities': total_chunk_entities,
                'chunk_results': chunk_results
            }

        except Exception as e:
            logger.error(f"❌ Knowledge graph storage failed: {e}")
            return {'count': 0, 'success': False, 'error': str(e)}
    
    async def _extract_metadata(self, file_path: Path, text: str) -> Dict[str, Any]:
        """Extract metadata from document."""
        try:
            return {
                'file_size': file_path.stat().st_size,
                'file_type': file_path.suffix,
                'text_length': len(text),
                'created_date': datetime.fromtimestamp(file_path.stat().st_ctime).isoformat(),
                'modified_date': datetime.fromtimestamp(file_path.stat().st_mtime).isoformat()
            }
        except Exception as e:
            logger.error(f"❌ Metadata extraction failed: {e}")
            return {}
    
    async def _check_if_already_processed(self, file_path: Path) -> Optional[Dict[str, Any]]:
        """Check if document was already processed."""
        try:
            # Check if aggressive CSV file exists
            doc_id = file_path.stem.split('_')[0] if '_' in file_path.stem else file_path.stem
            ref_dir = Path("references")
            
            for csv_file in ref_dir.glob(f"{doc_id}*_aggressive_references.csv"):
                logger.info(f"📄 Found existing processing: {csv_file.name}")
                
                # Count references in existing CSV
                try:
                    with open(csv_file, 'r', encoding='utf-8') as f:
                        reader = csv.reader(f)
                        ref_count = len(list(reader)) - 1  # Subtract header
                    
                    return {
                        'success': True,
                        'filename': file_path.name,
                        'references': ref_count,
                        'already_processed': True,
                        'csv_path': str(csv_file)
                    }
                except Exception:
                    continue
            
            return None
        except Exception:
            return None
    
    def _create_error_result(self, error_message: str) -> Dict[str, Any]:
        """Create standardized error result."""
        return {
            'success': False,
            'error': error_message,
            'chunks': 0,
            'entities': 0,
            'references': 0,
            'processing_time': datetime.now().isoformat()
        }

# Global instance
_unified_pipeline = None

async def get_unified_pipeline() -> UnifiedIngestionPipeline:
    """Get the global unified pipeline instance."""
    global _unified_pipeline
    if _unified_pipeline is None:
        _unified_pipeline = UnifiedIngestionPipeline()
    return _unified_pipeline
