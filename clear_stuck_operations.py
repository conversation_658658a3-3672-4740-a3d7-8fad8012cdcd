#!/usr/bin/env python3
"""
Clear stuck operations and reset the system.
"""

import asyncio
import redis
import json
from database.falkordb_adapter import get_falkordb_adapter

async def clear_stuck_operations():
    """Clear any stuck operations from Redis and reset the system."""
    
    print("🧹 Clearing stuck operations...")
    
    try:
        # Connect to Redis
        redis_client = redis.Redis(host='localhost', port=6380, decode_responses=True)
        
        # Clear all operation-related keys
        operation_keys = redis_client.keys("operation:*")
        if operation_keys:
            redis_client.delete(*operation_keys)
            print(f"✅ Cleared {len(operation_keys)} operation keys from Redis")
        
        # Clear WebSocket-related keys
        websocket_keys = redis_client.keys("websocket:*")
        if websocket_keys:
            redis_client.delete(*websocket_keys)
            print(f"✅ Cleared {len(websocket_keys)} WebSocket keys from Redis")
        
        # Clear any progress tracking keys
        progress_keys = redis_client.keys("progress:*")
        if progress_keys:
            redis_client.delete(*progress_keys)
            print(f"✅ Cleared {len(progress_keys)} progress keys from Redis")
        
        # Clear active operations
        active_keys = redis_client.keys("active:*")
        if active_keys:
            redis_client.delete(*active_keys)
            print(f"✅ Cleared {len(active_keys)} active operation keys from Redis")
        
        print("✅ All stuck operations cleared!")
        
    except Exception as e:
        print(f"❌ Error clearing Redis operations: {e}")
    
    try:
        # Check database connection
        adapter = await get_falkordb_adapter()
        
        # Test query
        result = adapter.execute_cypher("MATCH (e:Episode) RETURN count(e) as count")
        doc_count = result[1][0][0] if result and len(result) > 1 else 0
        print(f"📊 Database connection OK - {doc_count} documents in database")
        
    except Exception as e:
        print(f"❌ Database connection error: {e}")

if __name__ == "__main__":
    asyncio.run(clear_stuck_operations())
